# beautiful_soup.py

from bs4 import BeautifulSoup, XMLParsedAsHTMLWarning
import sys
import os
import warnings

# Filter out the XMLParsedAsHTMLWarning as suggested
warnings.filterwarnings("ignore", category=XMLParsedAsHTMLWarning)

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from utils import clean_soup, get_text_from_soup, extract_title, get_relevant_images
from typing import Tuple, List, Dict, Any

class BeautifulSoupScraper:
    def __init__(self, link: str, session=None):
        self.link = link
        self.session = session

    def scrape(self) -> Tuple[str, List[Dict[str, Any]], str]:
        """
        Fetch the page, parse it, clean it, and extract:
          - main text content (string)
          - list of (url,score) images
          - page title
        If anything goes wrong, returns ("", [], "").
        """
        try:
            resp = self.session.get(self.link, timeout=4)
            soup = BeautifulSoup(resp.content, "lxml", from_encoding=resp.encoding)
            soup = clean_soup(soup)
            text = get_text_from_soup(soup)
            images = get_relevant_images(soup, self.link)
            title = extract_title(soup)
            return text, images, title
        except Exception as e:
            print(f"[BeautifulSoupScraper] Error scraping {self.link}: {e}")
            return "", [], ""