# Use a Python base image
FROM python:3.12.11-slim

# Set environment variables for Python
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
#  - ffmpeg, libsndfile1 for audio I/O
#  - build-essential only if you ever need to compile anything locally
RUN apt-get update \
 && apt-get install -y --no-install-recommends \
      ffmpeg \
      libsndfile1 \
      build-essential \
 && rm -rf /var/lib/apt/lists/*

# Set up a working directory for your application
WORKDIR /app

# Copy application code into the container
COPY . /app

# Upgrade pip and install Python dependencies
RUN pip install --upgrade pip

# Install your individual livekit-plugin packages first (if you really need them separately)
# RUN pip install "livekit-agents[bithuman]~=1.0"
RUN pip install anyio python-dotenv

RUN pip install livekit-plugins-openai
RUN pip install "livekit-agents[azure]"
RUN pip install \
      livekit-plugins-noise-cancellation \
      livekit-plugins-azure \
      googlesearch-python \
      livekit-plugins-turn-detector

# Install the rest of your requirements
RUN pip install --no-cache-dir -r requirements.txt

# Install CPU-only PyTorch (torch, torchvision, torchaudio)
RUN pip install --no-cache-dir \
      torch torchvision torchaudio \
      --extra-index-url https://download.pytorch.org/whl/cpu

# Download any files your app needs at build time
RUN python main.py download-files

# Step 6: Specify the command to run your application
CMD ["python", "main.py", "start"]