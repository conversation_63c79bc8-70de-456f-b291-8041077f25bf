#!/usr/bin/env python3
import asyncio
import logging
import os
import sys
import yaml
from dataclasses import dataclass, field
from datetime import datetime
from typing import Annotated, Optional
import requests
import anyio
import json
from dotenv import load_dotenv
from beautiful_soup import BeautifulSoupScraper
from googlesearch import search
from pydantic import Field
from livekit.agents.llm import function_tool as llm_function_tool
from livekit.plugins.turn_detector.multilingual import MultilingualModel
from livekit.plugins import (
    azure,
    silero,
    openai,
    noise_cancellation,
)
try:
    from livekit.agents import (
        Agent,
        AgentSession,
        JobContext,
        RunContext,
        ToolError,
        WorkerOptions,
        cli,
        function_tool,
        RoomInputOptions,
        RoomOutputOptions,
    )
except ImportError as e:
    print(f"Import error: {e}")
    print("PATH: " + os.environ.get('PYTHONPATH', 'Not set'))
    print("Current directory: " + os.getcwd())
    sys.exit(1)

logger = logging.getLogger("pepgenx-example")
logger.setLevel(logging.INFO)

if not logger.hasHandlers():
    handler = logging.StreamHandler()
    formatter = logging.Formatter('[%(asctime)s %(levelname)s] %(name)s: %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

load_dotenv()

time_now = datetime.now()

SERVICES_CATALOG = {
    "Consulting Session": 500.0,
    "Custom Architecture Design": 1000.0,
    "Development Package": 2500.0,
    "Deployment Service": 1500.0,
}

@dataclass
class UserData:
    customer_name: Optional[str] = None
    customer_email: Optional[str] = None
    meeting_date: Optional[str] = None
    meeting_time: Optional[str] = None
    selected_services: Optional[list[str]] = None
    expense: Optional[float] = None
    payment_info: dict = field(default_factory=dict)
    checked_out: Optional[bool] = None
    agents: dict[str, Agent] = field(default_factory=dict)
    prev_agent: Optional[Agent] = None

    def summarize(self) -> str:
        data = {
            "name": self.customer_name or "unknown",
            "email": self.customer_email or "unknown",
            "meeting_date": self.meeting_date or "none",
            "meeting_time": self.meeting_time or "none",
            "services": self.selected_services or [],
            "expense": self.expense or 0.0,
            "payment_info": {
                "card_number": bool(self.payment_info.get("number")),
                "expiry": bool(self.payment_info.get("expiry")),
                "cvv": bool(self.payment_info.get("cvv")),
            },
            "checked_out": self.checked_out or False,
        }
        return yaml.dump(data)

RunContext_T = RunContext[UserData]

# --------------------------------------------------------------------------
# --- COMMON TOOLS FOR UPDATING USER DATA
# --------------------------------------------------------------------------

@function_tool()
async def update_name(
    name: Annotated[str, Field(description="The visitor's full name")],
    context: RunContext_T,
) -> str:
    context.userdata.customer_name = name
    return f"Got it—your name is {name}."

@function_tool()
async def update_email(
    email: Annotated[str, Field(description="The visitor's email address")],
    context: RunContext_T,
) -> str:
    context.userdata.customer_email = email
    return f"Your email is set to {email}."

@function_tool()
async def to_greeter(context: RunContext_T) -> Agent:
    """Return to the main greeter at any time."""
    current = context.session.current_agent
    next_agent = context.userdata.agents["greeter"]
    context.userdata.prev_agent = current
    return next_agent

@function_tool()
async def to_search(context: RunContext_T) -> Agent:
    """Switch to search information agent at any time."""
    current = context.session.current_agent
    next_agent = context.userdata.agents["search_information"]
    context.userdata.prev_agent = current
    return next_agent

@function_tool()
async def to_scheduler(context: RunContext_T) -> Agent:
    """Switch to the pepgpt meeting scheduler agent at any time."""
    current = context.session.current_agent
    next_agent = context.userdata.agents["meeting_scheduler"]
    context.userdata.prev_agent = current
    return next_agent

@function_tool()
async def to_purchase(context: RunContext_T) -> Agent:
    """Switch to the pepgpt purchase services agent at any time."""
    current = context.session.current_agent
    next_agent = context.userdata.agents["purchase_service"]
    context.userdata.prev_agent = current
    return next_agent

# --------------------------------------------------------------------------
# --- BASE AGENT CLASS: handles chat history stitching and user data context
# --------------------------------------------------------------------------

class BaseAgent(Agent):
    async def on_enter(self) -> None:
        agent_name = self.__class__.__name__
        logger.info(f"Entering agent: {agent_name}")

        # 1) make a mutable copy of the incoming read‐only context
        new_ctx = self.chat_ctx.copy(exclude_instructions=True)

        # 2) stitch in a bit of the previous agent’s history if any
        userdata = self.session.userdata
        if isinstance(userdata.prev_agent, Agent):
            prev_ctx = (
                userdata.prev_agent.chat_ctx
                .copy(exclude_instructions=True)
                .truncate(max_items=6)
            )
            new_ctx.items.extend(prev_ctx.items)

        # 3) inject the system prompt + user data
        new_ctx.add_message(
            role="system",
            content=(
                f"You are the {agent_name}.  Current user data:\n"
                f"{userdata.summarize()}"
            ),
        )

        # 4) push that edited context back into the agent
        await self.update_chat_ctx(new_ctx)

        # kick off the reply as you had before
        self.session.generate_reply(tool_choice="none")

    async def _transfer(
        self, target: str, context: RunContext_T
    ) -> tuple[Agent, str]:
        userdata = context.userdata
        current = context.session.current_agent
        next_agent = userdata.agents[target]
        userdata.prev_agent = current
        return next_agent, f"Transferring you to our {target} agent now."

# --------------------------------------------------------------------------
# --- GREETER: choose meeting scheduling vs. purchase
# --------------------------------------------------------------------------

class Greeter(BaseAgent):
    def __init__(self):
        instructions = (
            "You are PepGenie the PepGenX front-desk assistant. "
            "You can speak in multiple languages. "
            "Advice visitors to change language from english if needed by saying 'Change language to' and than provide language that you want me to use. "            
            "You welcome each visitor and ask whether they’d like to:\n"
            "  1) Schedule a meeting with our PepGenX team\n"
            "  2) Purchase one or more PepGenX services\n"
            "  3) Search for information using public internet or PepsiCo's internal knowledge base\n"
            "Use the provided tools to route them to the correct agent."
        )
        super().__init__(
            instructions=instructions,
            llm=openai.realtime.RealtimeModel.with_azure(
                azure_deployment="gpt-4o-mini-realtime-preview",
                azure_endpoint="wss://dariu-m5ofoh68-eastus2.openai.azure.com/",
                api_key="7Wjot1Ci3wAMfZGfaDn7qoAPriTNCe45VjQWeLmTw9BACJhRiRF7JQQJ99BAACHYHv6XJ3w3AAAAACOGqhhZ",
                api_version="2024-10-01-preview",
            ),
            tts=azure.TTS(
                voice="en-US-NancyNeural",
                speech_region="eastus2",
                speech_key="39228418150f4e2082fe2adfcd5c185b",
            ),
        )

    @function_tool()
    async def to_schedule_meeting(self, context: RunContext_T) -> tuple[Agent, str]:
        return await self._transfer("meeting_scheduler", context)

    @function_tool()
    async def to_purchase_services(self, context: RunContext_T) -> tuple[Agent, str]:
        return await self._transfer("purchase_services", context)

    @function_tool()
    async def to_search_information(self, context: RunContext_T) -> tuple[Agent, str]:
        return await self._transfer("search_information", context)

    @function_tool()
    async def to_welcome_agent(self, context: RunContext_T) -> tuple[Agent, str]:
        return await self._transfer("greeter", context)

# --------------------------------------------------------------------------
# --- MEETING SCHEDULER
# --------------------------------------------------------------------------

class MeetingScheduler(BaseAgent):
    def __init__(self):
        instructions = (
            "You are the PepGenX Meeting Scheduler.  "
            "Ask the visitor for a preferred meeting date and time, "
            "then collect their name and email, and confirm the appointment."
        )
        super().__init__(
            instructions=instructions,
            tools=[update_name, update_email, to_greeter, to_search, to_scheduler, to_purchase],
            tts=azure.TTS(
                voice="en-US-NancyNeural",
                speech_region="eastus2",
                speech_key="39228418150f4e2082fe2adfcd5c185b",
            ),
        )

    @function_tool()
    async def update_meeting_date(
        self,
        date: Annotated[str, Field(description="Desired meeting date (e.g. 2024-07-15)")],
        context: RunContext_T,
    ) -> str:
        context.userdata.meeting_date = date
        return f"Scheduled date set to {date}."

    @function_tool()
    async def update_meeting_time(
        self,
        time: Annotated[str, Field(description="Desired meeting time (e.g. 14:00)")],
        context: RunContext_T,
    ) -> str:
        context.userdata.meeting_time = time
        return f"Scheduled time set to {time}."

    @function_tool()
    async def confirm_meeting(self, context: RunContext_T) -> str | tuple[Agent, str]:
        ud = context.userdata
        missing = []
        if not ud.meeting_date:
            missing.append("date")
        if not ud.meeting_time:
            missing.append("time")
        if not ud.customer_name:
            missing.append("name")
        if not ud.customer_email:
            missing.append("email")
        if missing:
            return "Please provide: " + ", ".join(missing)
        return await self._transfer("greeter", context)

# --------------------------------------------------------------------------
# --- PURCHASE SERVICES AGENT
# --------------------------------------------------------------------------

class PurchaseServices(BaseAgent):
    def __init__(self):
        catalog_text = "\n".join(f"{k}: ${v}" for k, v in SERVICES_CATALOG.items())
        instructions = (
            "You are the PepGenX Sales Agent.  Present our services catalog:\n"
            f"{catalog_text}\n"
            "Ask the visitor which services they'd like, accept multiple selections, "
            "and send them to checkout."
        )
        super().__init__(
            instructions=instructions,
            tools=[to_greeter, to_search, to_scheduler, to_purchase],
            tts=azure.TTS(
                voice="en-US-NancyNeural",
                speech_region="eastus2",
                speech_key="39228418150f4e2082fe2adfcd5c185b",
            ),
        )

    @function_tool()
    async def update_selection(
        self,
        services: Annotated[list[str], Field(description="List of chosen PepGenX services")],
        context: RunContext_T,
    ) -> str:
        ud = context.userdata
        ud.selected_services = services
        # compute total
        ud.expense = sum(SERVICES_CATALOG.get(s, 0.0) for s in services)
        return f"Your selected services: {services}. Total: ${ud.expense:.2f}"

    @function_tool()
    async def to_checkout(self, context: RunContext_T) -> tuple[Agent, str]:
        ud = context.userdata
        if not ud.selected_services:
            return "Please pick at least one service first."
        return await self._transfer("checkout", context)

# --------------------------------------------------------------------------
# --- CHECKOUT AGENT: collect payment
# --------------------------------------------------------------------------

class Checkout(BaseAgent):
    def __init__(self):
        instructions = (
            "You are the PepGenX Checkout Agent.  You confirm the total cost and "
            "collect payment details (card number, expiry, CVV).  "
            "After successful capture, return visitor to the greeter."
        )
        super().__init__(
            instructions=instructions,
            tools=[update_name, update_email, to_greeter, to_search, to_scheduler, to_purchase],
            tts=azure.TTS(
                voice="en-US-NancyNeural",
                speech_region="eastus2",
                speech_key="39228418150f4e2082fe2adfcd5c185b",
            ),
        )

    @function_tool()
    async def confirm_total(
        self,
        total: Annotated[float, Field(description="Total amount to charge")],
        context: RunContext_T,
    ) -> str:
        context.userdata.expense = total
        return f"Confirmed total: ${total:.2f}"

    @function_tool()
    async def update_payment(
        self,
        number: Annotated[str, Field(description="Credit card number")],
        expiry: Annotated[str, Field(description="Expiry Date")],
        cvv: Annotated[str, Field(description="CVV")],
        context: RunContext_T,
    ) -> str:
        pi = context.userdata.payment_info
        pi["number"], pi["expiry"], pi["cvv"] = number, expiry, cvv
        return "Payment details recorded."

    @function_tool()
    async def confirm_payment(self, context: RunContext_T) -> str | tuple[Agent, str]:
        ud = context.userdata
        if ud.expense is None:
            return "Please confirm the total first."
        pi = ud.payment_info
        if not (pi.get("number") and pi.get("expiry") and pi.get("cvv")):
            return "Please provide complete payment details."
        ud.checked_out = True
        return await to_greeter(context)

# --------------------------------------------------------------------------
# --- SEARCH INFORMATION AGENT
# --------------------------------------------------------------------------

class SearchInformation(BaseAgent):
    def __init__(self):
        instructions = (
            f"Current date is {time_now.year} year, {time_now.month} month, {time_now.day} day, and the time is {time_now.strftime('%H:%M:%S')}. "
            "You are a voice-based personal assistant for a very busy person, who prefers concise, clear messages without unnecessary details. "
            "Always speak naturally, clearly, and concisely. Avoid any markdown formatting, symbols, bullet points, or punctuation that might complicate pronunciation or comprehension in audio form. "
            "You are tasked to produce responses adapted specifically for high-quality text-to-speech audio using Azure TTS. Adjust your tone, pitch, rhythm, and emphasis to communicate naturally and dynamically according to the intended emotional and communicative context relevant to language used and culture. "
            "If the user explicitly asks for more details or an elaborate explanation, provide a longer, detailed response; otherwise, always give concise and specific information. "
            "If you encounter a user question and you are not fully confident in your answer, clearly "
            "inform the user you will verify this information using a tool 'search_web' for searching publicly-available web information or a tool 'search_local' for searching inside PepsiCo's internal database. "
            "Before initiating the web search, explicitly say 'I will now search this information using Internet' for loca search say 'I will now search this information using PepsiCo's internal database'. "
            "When you receive a response back from the choosen tool, immediately say clearly 'I'm now thinking and starting to prepare the answer' and start working on your answer providng it as soon as possible. "
            "After providing the response, explicitly confirm the exact source of your answer by clearly saying if it was retrieved from the public internet or from PepsiCo's internal knowledge base. "
            "Never say you don’t have the answer without first attempting a tool search. "
            "Never makupe up an answer. User one of the search tools or both search tools to gather information and answer the question. "
            "If there is any brief silent moment, announce clearly to the user what you are currently doing so they remain informed."
        )

        @function_tool()
        async def search_web(
            ctx: RunContext,
            query: str,
            top_n: int = 10,
            language: str = 'en',
        ) -> str:
            """Retrieves information from public internet using google search and website scraping.
            
            Args:
                query: User query
                top_n: Maximum number of results to retireve from search engine
                language: Language in which query should be performed. If not provided english language will be used.
            
            Returns:
                Information retrieved from websites provided by google search.
            """

            # await ctx.session.say(
            #     # f"Searching...",
            #     f"Szukam...",
            #     allow_interruptions=False,
            # )

            session = requests.Session()
            session.headers.update({
                "User-Agent": (
                    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64) "
                    "AppleWebKit/537.36 (KHTML, like Gecko) "
                    "Chrome/120.0.0.0 Safari/537.36 "
                    "chat.pepgenx.dev"
                )
            })
            
            api_key = os.getenv("GOOGLE_API_KEY")
            cx      = os.getenv("GOOGLE_CX_KEY")
            if not api_key or not cx:
                return "Google CSE credentials not set."

            def _fetch():
                url = "https://www.googleapis.com/customsearch/v1"
                params = {
                    "key": api_key, 
                    "cx": cx, 
                    "q": query, 
                    "num": top_n, 
                    "lr": f"lang_{language}"
                    }
                r = session.get(url, params=params)
                r.raise_for_status()
                return r.json()

            data = await anyio.to_thread.run_sync(_fetch)
            items = data.get("items", [])

            all_results = []

            # for it in items:
            #     link    = it.get("link")
            #     scraper = BeautifulSoupScraper(link=link, session=session)
            #     content, images, title2 = await anyio.to_thread.run_sync(scraper.scrape)
            #     snippet2 = content[:5000].replace("\n"," ") + "…"
            #     all_results.append(f"{title2 or link}\n{snippet2}")

            # final = "\n\n".join(all_results)
            # return final


            # all_results = []

            async def scrape_url(url: str) -> str:
                try:
                    scraper = BeautifulSoupScraper(link=url, session=session)
                    content, images, title = await anyio.to_thread.run_sync(scraper.scrape)
                    snippet = content.strip().replace("\n", " ")
                    snippet = snippet[:5000] + ("…" if len(snippet) > 5000 else "")
                    header = title or url
                    result = f"{header}\n{snippet}"
                    return result
                except Exception as e:
                    error_msg = f"{url}\nFailed to scrape: {e}"
                    return error_msg

            # # Process URLs one by one to ensure streaming updates in order
            # count = len(urls)
            # await ctx.session.say(
            #     f"Found {count} website on the internet. Retrieving information...",
            #     allow_interruptions=False,
            # )
            # for url in items:
            #     result = await scrape_url(url)
            #     all_results.append(result)
            
            for item in items:
                link = item.get("link")
                if link and link.startswith("http"):
                    all_results.append(await scrape_url(link))

            # Final compilation of all results
            final_result = "\n\n".join(all_results)
            print(final_result)

            return final_result

        @function_tool()
        async def search_local(
            ctx: RunContext,
            query: str,
            library: str,
        ) -> str:
            """
            Retrieves information from all PepsiCo knowledge bases.
            Possible values of library argument:
                - argument value 'c6af20c3-edf1-4d36-ac5b-174a5fe59efc' will use knowledge base that stores information about PepsiCo cloud services, aproved cloud architecture and configuration patterns, cloud foundation networking and everything related to technical side of cloud configuration and approved patterns
                - argument value '32b0470f-7fe0-4de5-a9c3-d3e44417b347' will use knowledge base that stores information about CMP (Cloud Marketplace),  Cloud Onboarding process and tools related questions
                - argument value '352fd45d-7e25-47be-bd2a-29f3fb8a69a6' will use knowledge base that stores information about PepGenX and GenerativeAI platform related questions
            
            Args:
                query: User query
                library: knowledge base that should be used for search.
            
            Returns:
                Information found inside PepsiCo knowledge bases.
            """

            headers = {
                "Authorization": "Bearer sk-ede826cfbdb0479fac8604a20d58a7d6",
                "Content-Type": "application/json",
            }

            # Build payload
            payload = {
                "model": "gpt-4.1",
                "messages": [
                    {"role": "user", "content": query}
                ],
                "files": [
                    {"type": "collection", "id": library}
                ]
            }

            # POST as a streaming request
            # await ctx.session.say(
            #     f"Przeszukję bibliotekę PepsiCo...",
            #     # f"I am searching PepsiCo knowledge base...",
            #     allow_interruptions=False,
            # )
            resp = requests.post(
                "https://chat.pepgenx.dev/api/chat/completions",
                headers=headers,
                json=payload,
                stream=True,
            )
            resp.raise_for_status()
            
            # Accumulate all assistant content pieces
            # await ctx.session.say(
            #     # f"Preparing your answer...",
            #     f"Przygotowuję odpowiedź...",
            #     allow_interruptions=False,
            # )
            final_result = ""
            for line in resp.iter_lines(decode_unicode=True):
                if not line or not line.startswith("data:"):
                    continue

                # Strip the "data: " prefix
                data_str = line[len("data:"):].strip()
                if data_str == "[DONE]":
                    break

                # Parse and extract the delta content
                chunk = json.loads(data_str)
                for choice in chunk.get("choices", []):
                    delta = choice.get("delta", {})
                    content = delta.get("content")
                    if content:
                        final_result += content

            # Log & return
            print(final_result)
            return final_result

        super().__init__(
            instructions=instructions,
            tools=[search_web, search_local, to_greeter, to_scheduler, to_purchase],
            tts=azure.TTS(
                voice="en-US-NancyNeural",
                speech_region="eastus2",
                speech_key="39228418150f4e2082fe2adfcd5c185b",
            ),
        )

# --------------------------------------------------------------------------
# --- ENTRYPOINT
# --------------------------------------------------------------------------

async def entrypoint(ctx: JobContext):
    await ctx.connect()

    userdata = UserData()
    userdata.agents.update({
        "greeter":           Greeter(),
        "meeting_scheduler": MeetingScheduler(),
        "purchase_services": PurchaseServices(),
        "search_information": SearchInformation(),
        "checkout":          Checkout(),
    })

    session = AgentSession[UserData](
        userdata=userdata,
        stt=azure.STT(
            speech_region="eastus2",
            speech_key="39228418150f4e2082fe2adfcd5c185b",
        ),
        tts=azure.TTS(
            voice="en-US-NancyNeural",
            speech_region="eastus2",
            speech_key="39228418150f4e2082fe2adfcd5c185b",
        ),
        vad=silero.VAD.load(),
        turn_detection=MultilingualModel(),
        llm=openai.realtime.RealtimeModel.with_azure(
            azure_deployment="gpt-4o-mini-realtime-preview",
            azure_endpoint="wss://dariu-m5ofoh68-eastus2.openai.azure.com/",
            api_key="7Wjot1Ci3wAMfZGfaDn7qoAPriTNCe45VjQWeLmTw9BACJhRiRF7JQQJ99BAACHYHv6XJ3w3AAAAACOGqhhZ",
            api_version="2024-10-01-preview",  
        ),
        max_tool_steps=5,
    )

    await session.start(
        agent=userdata.agents["greeter"],
        room=ctx.room,
        room_input_options=RoomInputOptions(),
    )
    # # initial prompt
    # await session.current_agent.say(
    #     "Hello! Welcome to PepGenX.  Would you like to schedule a meeting with our team "
    #     "purchase one of our services or just search for information today? "
    #     "You can change language by saying 'Change language to' and than provide language that you want me to use.",
    #     allow_interruptions=False,
    # )


if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))