#!/usr/bin/env python3
import asyncio
import logging
import requests
import anyio
from dataclasses import dataclass
import os
import sys
import json
from datetime import datetime

from dotenv import load_dotenv
from livekit.plugins import (
    azure,
    silero,
    openai,
    noise_cancellation,
)
from livekit.plugins.turn_detector.multilingual import MultilingualModel
try:
    from livekit.agents import (
        Agent,
        AgentSession,
        JobContext,
        RunContext,
        ToolError,
        WorkerOptions,
        cli,
        function_tool,
        RoomInputOptions,
        RoomOutputOptions,
    )

except ImportError as e:
    print(f"Import error: {e}")
    print("PATH: " + os.environ.get('PYTHONPATH', 'Not set'))
    print("Current directory: " + os.getcwd())
    sys.exit(1)

from beautiful_soup import BeautifulSoupScraper
from googlesearch import search

load_dotenv()

logger = logging.getLogger("web_search")
logger.setLevel(logging.INFO)
if not logger.hasHandlers():
    handler = logging.StreamHandler()
    formatter = logging.Formatter('[%(asctime)s %(levelname)s] %(name)s: %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

@function_tool()
async def search_web(
    ctx: RunContext,
    query: str,
    top_n: int = 10,
    language: str = 'en',
) -> str:
    """Retrieves information from public internet using google search and website scraping.
    
    Args:
        query: User query
        top_n: Maximum number of results to retireve from search engine
        language: Language in which query should be performed. If not provided english language will be used.
    
    Returns:
        Information retrieved from websites provided by google search.
    """

    # await ctx.session.say(
    #     # f"Searching...",
    #     f"Szukam...",
    #     allow_interruptions=False,
    # )

    session = requests.Session()
    session.headers.update({
        "User-Agent": (
            "Mozilla/5.0 (X11; Ubuntu; Linux x86_64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/120.0.0.0 Safari/537.36 "
            "chat.pepgenx.dev"
        )
    })
    
    api_key = os.getenv("GOOGLE_API_KEY")
    cx      = os.getenv("GOOGLE_CX_KEY")
    if not api_key or not cx:
        return "Google CSE credentials not set."

    def _fetch():
        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            "key": api_key, 
            "cx": cx, 
            "q": query, 
            "num": top_n, 
            "lr": f"lang_{language}"
            }
        r = session.get(url, params=params)
        r.raise_for_status()
        return r.json()

    data = await anyio.to_thread.run_sync(_fetch)
    items = data.get("items", [])

    all_results = []

    # for it in items:
    #     link    = it.get("link")
    #     scraper = BeautifulSoupScraper(link=link, session=session)
    #     content, images, title2 = await anyio.to_thread.run_sync(scraper.scrape)
    #     snippet2 = content[:5000].replace("\n"," ") + "…"
    #     all_results.append(f"{title2 or link}\n{snippet2}")

    # final = "\n\n".join(all_results)
    # return final


    # all_results = []

    async def scrape_url(url: str) -> str:
        try:
            scraper = BeautifulSoupScraper(link=url, session=session)
            content, images, title = await anyio.to_thread.run_sync(scraper.scrape)
            snippet = content.strip().replace("\n", " ")
            snippet = snippet[:5000] + ("…" if len(snippet) > 5000 else "")
            header = title or url
            result = f"{header}\n{snippet}"
            return result
        except Exception as e:
            error_msg = f"{url}\nFailed to scrape: {e}"
            return error_msg

    # # Process URLs one by one to ensure streaming updates in order
    # count = len(urls)
    # await ctx.session.say(
    #     f"Found {count} website on the internet. Retrieving information...",
    #     allow_interruptions=False,
    # )
    # for url in items:
    #     result = await scrape_url(url)
    #     all_results.append(result)
    
    for item in items:
        link = item.get("link")
        if link and link.startswith("http"):
            all_results.append(await scrape_url(link))

    # Final compilation of all results
    final_result = "\n\n".join(all_results)
    print(final_result)

    return final_result

@function_tool()
async def search_local(
    ctx: RunContext,
    query: str,
    library: str,
) -> str:
    """
    Retrieves information from all PepsiCo knowledge bases.
    Possible values of library argument:
        - argument value 'c6af20c3-edf1-4d36-ac5b-174a5fe59efc' will use knowledge base that stores information about PepsiCo cloud services, aproved cloud architecture and configuration patterns, cloud foundation networking and everything related to technical side of cloud configuration and approved patterns
        - argument value '32b0470f-7fe0-4de5-a9c3-d3e44417b347' will use knowledge base that stores information about CMP (Cloud Marketplace),  Cloud Onboarding process and tools related questions
        - argument value '352fd45d-7e25-47be-bd2a-29f3fb8a69a6' will use knowledge base that stores information about PepGenX and GenerativeAI platform related questions
    
    Args:
        query: User query
        library: knowledge base that should be used for search.
    
    Returns:
        Information found inside PepsiCo knowledge bases.
    """

    headers = {
        "Authorization": "Bearer sk-ede826cfbdb0479fac8604a20d58a7d6",
        "Content-Type": "application/json",
    }

    # Build payload
    payload = {
        "model": "gpt-4.1",
        "messages": [
            {"role": "user", "content": query}
        ],
        "files": [
            {"type": "collection", "id": library}
        ]
    }

    # POST as a streaming request
    # await ctx.session.say(
    #     f"Przeszukję bibliotekę PepsiCo...",
    #     # f"I am searching PepsiCo knowledge base...",
    #     allow_interruptions=False,
    # )
    resp = requests.post(
        "https://chat.pepgenx.dev/api/chat/completions",
        headers=headers,
        json=payload,
        stream=True,
    )
    resp.raise_for_status()
    
    # Accumulate all assistant content pieces
    # await ctx.session.say(
    #     # f"Preparing your answer...",
    #     f"Przygotowuję odpowiedź...",
    #     allow_interruptions=False,
    # )
    final_result = ""
    for line in resp.iter_lines(decode_unicode=True):
        if not line or not line.startswith("data:"):
            continue

        # Strip the "data: " prefix
        data_str = line[len("data:"):].strip()
        if data_str == "[DONE]":
            break

        # Parse and extract the delta content
        chunk = json.loads(data_str)
        for choice in chunk.get("choices", []):
            delta = choice.get("delta", {})
            content = delta.get("content")
            if content:
                final_result += content

    # Log & return
    print(final_result)
    return final_result

async def entrypoint(ctx: JobContext):
    await ctx.connect()
    time_now = datetime.now()
    agent = Agent(instructions=(
            # f"Now is year={time_now.year}, month={time_now.month}, day={time_now.day}, current time={time_now.strftime('%H:%M:%S')}. "
            # "You are a personal assistant for a very busy person who doesn't have enough time to listen to overly long messages. "
            # "Be concise and specific unless the user asks you for more details and a longer response. "
            # # "Your primary language is English. If you're uncertain which language to use, always default to English. "
            # "Your primary language is Polish. If you're uncertain which language to use, always default to Polish. "
            # "You are a helpful assistant generating Polish-language text scripts that will be converted to audio using Azure TTS. "
            # "Adapt tone, pitch, rhythm, and emphasis according to the intended emotional and communicative context of the message, "
            # "ensuring that the output is natural, dynamic, and appropriate for the Polish language and cultural context. "
            # # ""
            # "You are interacting via voice, thus your responses must always be clear, concise, and precise. "
            # "Avoid all markdown, special symbols, bullet points, and punctuation difficult to pronounce during voice interaction. "
            # "Speak naturally, as you would when conversing directly with a person. "
            # "If the user explicitly requests additional details or an elaborate explanation, provide a more detailed answer. "
            # "When you encounter a question outside your existing knowledge or feel insufficient confidence, "
            # "inform the user clearly that you'll need to verify the information, then automatically invoke either the 'search_web' "
            # "tool to access public internet or the 'search_local' tool for PepsiCo's internal database. "
            # "Upon answering the user after search, always inform them explicitly whether the retrieved information came from "
            # "the public internet or PepsiCo's internal knowledge base. Never state you don't have the answer without first attempting to search. "
            # "Always keep the user informed of what you're doing if you become silent briefly."
            f"Current date is {time_now.year} year, {time_now.month} month, {time_now.day} day, and the time is {time_now.strftime('%H:%M:%S')}. "
            "You are a voice-based personal assistant for a very busy person, who prefers concise, clear messages without unnecessary details. Your name is PepGeni. "
            "Always speak naturally, clearly, and concisely. Avoid any markdown formatting, symbols, bullet points, or punctuation that might complicate pronunciation or comprehension in audio form. "
            "Your primary language is French. Always use French unless explicitly instructed otherwise. "
            "You are tasked to produce responses adapted specifically for high-quality text-to-speech audio using Azure TTS. Adjust your tone, pitch, rhythm, and emphasis to communicate naturally and dynamically according to the intended emotional and communicative context relevant to language used and culture. "
            "If the user explicitly asks for more details or an elaborate explanation, provide a longer, detailed response; otherwise, always give concise and specific information. "
            "If you encounter a user question outside your existing knowledge or you are not fully confident in your answer, clearly "
            "inform the user you will verify this information using a tool 'search_web' for searching publicly-available web information a tool 'search_local' for searching inside PepsiCo's internal database. "
            "Before initiating the web search, explicitly say 'I will now search this information using Internet' for loca search say 'I will now search this information using PepsiCo's internal database'. "
            "When you receive a response back from the choosen tool, immediately say clearly 'I'm now thinking and starting to prepare the answer' and start working on your answer providng it as soon as possible. "
            "After providing the response, explicitly confirm the exact source of your answer by clearly saying if it was retrieved from the public internet or from PepsiCo's internal knowledge base. "
            "Never say you don’t have the answer without first attempting a tool search. "
            "If there is any brief silent moment, announce clearly to the user what you are currently doing so they remain informed."
            ), 
            tools=[search_web,search_local])
    session = AgentSession(
        stt = azure.STT(
            speech_region="eastus2",
            speech_key="39228418150f4e2082fe2adfcd5c185b",
            language="pl-PL",
            ),
        # llm = openai.LLM.with_azure(
        #     azure_deployment="gpt-4.1-mini",
        #     azure_endpoint="https://dariu-m5ofoh68-eastus2.openai.azure.com/",
        #     api_key="7Wjot1Ci3wAMfZGfaDn7qoAPriTNCe45VjQWeLmTw9BACJhRiRF7JQQJ99BAACHYHv6XJ3w3AAAAACOGqhhZ",
        #     api_version="2025-01-01-preview",
        #     timeout=120.0,
        #     temperature=0,
        #     parallel_tool_calls=True,
        #     ),
        tts = azure.TTS(
            # voice="en-US-NancyNeural",
            voice="pl-PL-AgnieszkaNeural",
            speech_region="eastus2",
            speech_key="39228418150f4e2082fe2adfcd5c185b",
            ),       
        vad=silero.VAD.load(),
        turn_detection=MultilingualModel(),
        llm=openai.realtime.RealtimeModel.with_azure(
            azure_deployment="gpt-4o-mini-realtime-preview",
            azure_endpoint="wss://dariu-m5ofoh68-eastus2.openai.azure.com/",
            api_key="7Wjot1Ci3wAMfZGfaDn7qoAPriTNCe45VjQWeLmTw9BACJhRiRF7JQQJ99BAACHYHv6XJ3w3AAAAACOGqhhZ",
            api_version="2024-10-01-preview",
        ),
    )

    # avatar = bithuman.AvatarSession(
    #     model_path="./dating_coach.imx",
    # )
    # await avatar.start(session, room=ctx.room)
    
    await session.start(
        agent=agent, 
        room=ctx.room,
        room_input_options=RoomInputOptions(
            text_enabled=True,
            # noise_cancellation=noise_cancellation.BVC(), 
        ), 
        room_output_options=RoomOutputOptions(
            audio_enabled=True
        ),        
    )

    # await session.say(
    #     # f"I am Genie, PepGenie, your personal assistant from PepsiCo. How can I help you today?",
    #     f"Nazywam się Zuzia, jestem Twoją asystentką personalną. Jak mogę Ci dzisiaj pomóc?",
    #     allow_interruptions=False,        
    # )

if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))


