# my_agent.py

from dotenv import load_dotenv
import os
import requests
import anyio
from livekit import agents
from livekit.agents import (
    function_tool,
    RunContext,
    AgentSession,
    Agent,
    RoomInputOptions,
)
from livekit.plugins import (
    openai,
    cartesia,
    deepgram,
    noise_cancellation,
    silero,
)
from livekit.plugins.turn_detector.multilingual import MultilingualModel
from livekit.plugins import azure

# import scraper
from beautiful_soup import BeautifulSoupScraper
from googlesearch import search

load_dotenv()

@function_tool()
async def web_search(
    context: RunContext,
    query: str,
    domains: list[str] | None = None,
    top_n: int = 3,
) -> str:
    """
    Perform a Google Search, fetch and scrape the top N results,
    and return their cleaned text concatenated.
    """
    # 1) build query (optionally constrain to domains)
    if domains:
        query += " " + " OR ".join(f"site:{d}" for d in domains)

    # 2) grab top-N URLs
    urls = list(search(query, num_results=top_n))
    if not urls:
        return "No results found."

    # 3) prepare an HTTP session for scraping
    session = requests.Session()
    session.headers.update({
        # Some sites block the default python-requests UA
        "User-Agent": "PepGenie/1.0 (+https://yourdomain.example)"
    })

    # 4) for each URL, scrape text on a threadpool
    results = []
    for url in urls:
        scraper = BeautifulSoupScraper(link=url, session=session)
        try:
            # scrape() is blocking I/O, run it on a thread
            content, images, title = await anyio.to_thread.run_sync(scraper.scrape)
            snippet = content.strip().replace("\n", " ")
            # limit length so we don't overwhelm the LLM
            snippet = snippet[:2000] + ("…" if len(snippet) > 2000 else "")
            header = title or url
            results.append(f"### {header}\n{snippet}")
        except Exception as e:
            # if a scrape fails, note it, but carry on
            results.append(f"### {url}\nFailed to scrape: {e}")

    # 5) join and return
    return "\n\n".join(results)


class Assistant(Agent):
    def __init__(self) -> None:
        super().__init__(
            instructions="You are a helpful PepGenie voice AI assistant. Never use markdown in your answers. Your interface with users will be voice. You should use short and concise responses, and avoiding usage of unpronouncable punctuation. Remember you are talking to a person not writing.",
            tools=[web_search],
        )


async def entrypoint(ctx: agents.JobContext):
    await ctx.connect()
    session = AgentSession(
        # stt=deepgram.STT(model="nova-3", language="multi"),
        stt = azure.STT(
            speech_region="eastus",
            speech_key="4vGfpXPQl5WwuKB13rp18celklOncKxi6Lk0TR1BrspOgZe6ZtSiJQQJ99BDACYeBjFXJ3w3AAAYACOGhaqt",
            # Remove speech_host as it conflicts with speech_region
            ),
        llm=openai.LLM(model="gpt-4o-mini"),
        # tts=cartesia.TTS(),
        tts = azure.TTS(
            voice="en-US-AriaNeural",
            speech_region="eastus",
            speech_key="4vGfpXPQl5WwuKB13rp18celklOncKxi6Lk0TR1BrspOgZe6ZtSiJQQJ99BDACYeBjFXJ3w3AAAYACOGhaqt",
            ),       
        vad=silero.VAD.load(),
        turn_detection=MultilingualModel(),
    )
    await session.start(
        room=ctx.room,
        agent=Assistant(),
        room_input_options=RoomInputOptions(
            noise_cancellation=noise_cancellation.BVC(),
        ),
    )
    await session.generate_reply(
        instructions=(
            "Greet the user using the following sentence 'I am Genie, PepGenie' "
            "and offer your assistance. Never use markdown in your answers. "
            "Your interface with users will be voice. You should use short and concise "
            "responses, and avoiding usage of unpronouncable punctuation."
            "Remember you are talking to a person not writing."
            "You have access to a web search tool. Whenever you don't know the answer use internet serach to find information."
        )
    )


if __name__ == "__main__":
    agents.cli.run_app(agents.WorkerOptions(entrypoint_fnc=entrypoint))