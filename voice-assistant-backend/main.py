# my_agent.py

from dotenv import load_dotenv
import os
import requests
import anyio
import asyncio
import json
import logging
from datetime import datetime
from livekit import agents
from livekit.agents import (
    function_tool,
    RunContext,
    AgentSession,
    Agent,
    RoomInputOptions,
)
from livekit.plugins import (
    openai,
    cartesia,
    deepgram,
    noise_cancellation,
    silero,
)
from livekit.plugins.turn_detector.multilingual import MultilingualModel
from livekit.plugins import azure

# import scraper
from beautiful_soup import BeautifulSoupScraper
from googlesearch import search

# Store active tasks to prevent garbage collection
_active_tasks = []

# Global variable to store system prompt from frontend
_system_prompt = None

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def read_system_prompt_from_file():
    """
    Read system prompt from ./charles/systemprompt.txt file.
    Returns the content if file exists and is not empty, otherwise returns None.
    """
    try:
        file_path = "./charles/systemprompt.txt"
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    logger.info(f"Successfully read system prompt from {file_path}")
                    return content
                else:
                    logger.info(f"System prompt file {file_path} is empty")
                    return None
        else:
            logger.info(f"System prompt file {file_path} does not exist")
            return None
    except Exception as e:
        logger.error(f"Error reading system prompt file: {e}")
        return None



import requests

def add_file_to_knowledge(token, knowledge_id, file_id):
    url = "https://web.pepgenx.dev/api/v1/knowledge/183cb07e-4ff1-466e-af93-2ffec046e9d7/file/add"
    headers = {
        "Authorization": "Bearer sk-0458b2abbaf141af9cd1245a1ebad81f",
        'Content-Type': 'application/json'
    }
    data = {'file_id': file_id}
    response = requests.post(url, headers=headers, json=data)
    return response.json()


def upload_file_to_api(filename: str, participant_identity: str):
    """
    Upload a file to the API endpoint using POST request.
    Re-encodes binary files to UTF-8 to avoid Unicode decode errors.
    
    Args:
        filename: The name of the file to upload
        participant_identity: The identity of the participant who sent the file
    """
    try:
        import mimetypes
        import codecs
        import tempfile
        import os
        
        # API endpoint and headers
        url = "https://web.pepgenx.dev/api/v1/knowledge/183cb07e-4ff1-466e-af93-2ffec046e9d7/file/add"
        headers = {
            "Authorization": "Bearer sk-0458b2abbaf141af9cd1245a1ebad81f",
            'Content-Type': 'application/json'
        }
        
        logger.info(f"Uploading file '{filename}' to API endpoint for participant {participant_identity}")
        
        # Detect proper MIME type for the file
        mime_type, _ = mimetypes.guess_type(filename)
        if mime_type is None:
            mime_type = 'application/octet-stream'
            
        # Special handling for common document types
        if filename.lower().endswith('.docx'):
            mime_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        elif filename.lower().endswith('.pdf'):
            mime_type = 'application/pdf'
        elif filename.lower().endswith('.txt'):
            mime_type = 'text/plain'
            
        logger.info(f"Using MIME type: {mime_type} for file: {filename}")
        
        # Check if file is text-based or binary
        is_text_file = filename.lower().endswith(('.txt', '.csv', '.json', '.xml', '.html', '.md'))
        
        if is_text_file:
            # For text files, read and re-encode to UTF-8
            logger.info(f"Processing '{filename}' as text file, re-encoding to UTF-8")
            utf8_filename = f"{filename}_utf8"
            
            BLOCKSIZE = 1048576  # 1MB blocks
            try:
                # Try different source encodings
                source_encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                encoded_successfully = False
                
                for encoding in source_encodings:
                    try:
                        with codecs.open(filename, "r", encoding) as source_file:
                            with codecs.open(utf8_filename, "w", "utf-8") as target_file:
                                while True:
                                    contents = source_file.read(BLOCKSIZE)
                                    if not contents:
                                        break
                                    target_file.write(contents)
                        logger.info(f"Successfully re-encoded '{filename}' from {encoding} to UTF-8")
                        encoded_successfully = True
                        break
                    except UnicodeDecodeError:
                        continue
                
                if not encoded_successfully:
                    logger.warning(f"Could not decode '{filename}' with any encoding, treating as binary")
                    utf8_filename = filename
                else:
                    filename = utf8_filename
            except Exception as e:
                logger.warning(f"Error re-encoding '{filename}': {e}, treating as binary")
                utf8_filename = filename
        else:
            # For binary files, encode the content as base64 to make it UTF-8 safe
            logger.info(f"Processing '{filename}' as binary file, encoding as base64")
            import base64
            utf8_filename = f"{filename}_base64.txt"
            
            try:
                with open(filename, 'rb') as binary_file:
                    binary_content = binary_file.read()
                    base64_content = base64.b64encode(binary_content).decode('utf-8')
                    
                with open(utf8_filename, 'w', encoding='utf-8') as utf8_file:
                    utf8_file.write(base64_content)
                    
                logger.info(f"Successfully encoded '{filename}' as base64 UTF-8 file")
                filename = utf8_filename
                mime_type = 'text/plain'  # Base64 encoded files are text
            except Exception as e:
                logger.warning(f"Error encoding '{filename}' as base64: {e}, using original file")
        
        # Upload the UTF-8 encoded file
        with open(filename, 'rb') as file_obj:
            files = {
                'files': (filename, file_obj, mime_type)
            }
            
            # Send POST request to upload the file
            response = requests.post(url, headers=headers, files=files, timeout=30)
            
            # Log the response details for debugging
            logger.info(f"Upload response status: {response.status_code}")
            logger.info(f"Upload response headers: {dict(response.headers)}")
            
            # Clean up temporary UTF-8 file if created
            if filename != os.path.basename(filename) and filename.endswith(('_utf8', '_base64.txt')):
                try:
                    os.remove(filename)
                    logger.info(f"Cleaned up temporary file: {filename}")
                except Exception as e:
                    logger.warning(f"Could not clean up temporary file {filename}: {e}")
            
            if response.status_code >= 400:
                logger.error(f"Upload failed with status {response.status_code}: {response.text}")
                return {"status": "error", "message": f"HTTP {response.status_code}: {response.text}"}
            
            logger.info(f"File successfully uploaded to API. Response: {response.status_code}")
            
            # Try to parse JSON response, fallback to text if not JSON
            try:
                return response.json() if response.content else {"status": "success"}
            except ValueError:
                return {"status": "success", "response_text": response.text}
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Network error uploading file to API: {e}")
        return {"status": "error", "message": f"Network error: {str(e)}"}
    except Exception as e:
        logger.error(f"Unexpected error uploading file to API: {e}")
        return {"status": "error", "message": f"Unexpected error: {str(e)}"}

def upload_file_to_api_alternative(filename: str, participant_identity: str):
    """
    Alternative upload method with better error handling and debugging.
    
    Args:
        filename: The name of the file to upload
        participant_identity: The identity of the participant who sent the file
    """
    try:
        import mimetypes
        import base64
        
        # API endpoint and headers
        url = "https://web.pepgenx.dev/api/v1/knowledge/183cb07e-4ff1-466e-af93-2ffec046e9d7/file/add"
        headers = {
            "Authorization": "Bearer sk-0458b2abbaf141af9cd1245a1ebad81f",
            'Content-Type': 'application/json'
        }
        
        logger.info(f"Uploading file '{filename}' using alternative method for participant {participant_identity}")
        
        # Detect MIME type
        mime_type, _ = mimetypes.guess_type(filename)
        if mime_type is None:
            mime_type = 'application/octet-stream'
            
        # Special handling for common document types
        if filename.lower().endswith('.docx'):
            mime_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        elif filename.lower().endswith('.pdf'):
            mime_type = 'application/pdf'
        elif filename.lower().endswith('.txt'):
            mime_type = 'text/plain'
        
        logger.info(f"Alternative method using MIME type: {mime_type}")
        
        # Try different approaches
        approaches = [
            # Approach 1: Standard multipart upload with proper MIME type
            lambda: _try_multipart_upload(filename, mime_type, url, headers),
            # Approach 2: Base64 encoded upload
            lambda: _try_base64_upload(filename, mime_type, url, headers),
            # Approach 3: Raw binary upload
            lambda: _try_raw_upload(filename, mime_type, url, headers)
        ]
        
        for i, approach in enumerate(approaches, 1):
            try:
                logger.info(f"Trying approach {i}")
                result = approach()
                if result.get("status") == "success":
                    logger.info(f"Success with approach {i}")
                    return result
                else:
                    logger.warning(f"Approach {i} failed: {result.get('message', 'Unknown error')}")
            except Exception as e:
                logger.warning(f"Approach {i} threw exception: {e}")
                continue
        
        # If all approaches failed
        return {"status": "error", "message": "All upload approaches failed"}
        
    except Exception as e:
        logger.error(f"Alternative upload failed for file '{filename}': {e}")
        return {"status": "error", "message": f"Alternative upload error: {str(e)}"}

def _try_multipart_upload(filename: str, mime_type: str, url: str, headers: dict):
    """Try standard multipart upload with UTF-8 encoding"""
    import codecs
    import base64
    import tempfile
    import os
    
    # Check if file is text-based or binary
    is_text_file = filename.lower().endswith(('.txt', '.csv', '.json', '.xml', '.html', '.md'))
    upload_filename = filename
    
    try:
        if is_text_file:
            # For text files, ensure UTF-8 encoding
            utf8_filename = f"{filename}_utf8_temp"
            BLOCKSIZE = 1048576
            
            source_encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            for encoding in source_encodings:
                try:
                    with codecs.open(filename, "r", encoding) as source_file:
                        with codecs.open(utf8_filename, "w", "utf-8") as target_file:
                            while True:
                                contents = source_file.read(BLOCKSIZE)
                                if not contents:
                                    break
                                target_file.write(contents)
                    upload_filename = utf8_filename
                    break
                except UnicodeDecodeError:
                    continue
        else:
            # For binary files, encode as base64
            utf8_filename = f"{filename}_base64_temp.txt"
            with open(filename, 'rb') as binary_file:
                binary_content = binary_file.read()
                base64_content = base64.b64encode(binary_content).decode('utf-8')
                
            with open(utf8_filename, 'w', encoding='utf-8') as utf8_file:
                utf8_file.write(base64_content)
            
            upload_filename = utf8_filename
            mime_type = 'text/plain'
        
        # Upload the processed file
        with open(upload_filename, 'rb') as file_obj:
            files = {'files': (os.path.basename(filename), file_obj, mime_type)}
            response = requests.post(url, headers=headers, files=files, timeout=30)
            
            if response.status_code < 400:
                try:
                    return response.json() if response.content else {"status": "success", "method": "multipart"}
                except ValueError:
                    return {"status": "success", "method": "multipart", "response_text": response.text}
            else:
                return {"status": "error", "message": f"HTTP {response.status_code}: {response.text}"}
    
    finally:
        # Clean up temporary file
        if upload_filename != filename and os.path.exists(upload_filename):
            try:
                os.remove(upload_filename)
            except Exception:
                pass

def _try_base64_upload(filename: str, mime_type: str, url: str, headers: dict):
    """Try base64 encoded upload"""
    import base64
    
    with open(filename, 'rb') as file_obj:
        file_content = file_obj.read()
        encoded_content = base64.b64encode(file_content).decode('ascii')
        
        data = {
            'filename': filename,
            'content': encoded_content,
            'mime_type': mime_type,
            'encoding': 'base64'
        }
        
        response = requests.post(url, headers={**headers, 'Content-Type': 'application/json'}, 
                               json=data, timeout=30)
        
        if response.status_code < 400:
            try:
                return response.json() if response.content else {"status": "success", "method": "base64"}
            except ValueError:
                return {"status": "success", "method": "base64", "response_text": response.text}
        else:
            return {"status": "error", "message": f"HTTP {response.status_code}: {response.text}"}

def _try_raw_upload(filename: str, mime_type: str, url: str, headers: dict):
    """Try raw binary upload"""
    with open(filename, 'rb') as file_obj:
        file_content = file_obj.read()
        
        upload_headers = {
            **headers,
            'Content-Type': mime_type,
            'Content-Disposition': f'attachment; filename="{filename}"'
        }
        
        response = requests.post(url, headers=upload_headers, data=file_content, timeout=30)
        
        if response.status_code < 400:
            try:
                return response.json() if response.content else {"status": "success", "method": "raw"}
            except ValueError:
                return {"status": "success", "method": "raw", "response_text": response.text}
        else:
            return {"status": "error", "message": f"HTTP {response.status_code}: {response.text}"}

# async def handle_data_packet(data: bytes, participant_identity: str):
#     """Handle data packets from frontend, including system prompt"""
#     global _system_prompt
#     try:
#         # Decode the data packet
#         data_str = data.decode('utf-8')
#         data_json = json.loads(data_str)

#         # Check if this is a system prompt packet
#         if data_json.get('type') == 'system_prompt':
#             _system_prompt = data_json.get('prompt', '')
#             logger.info(f"Received system prompt from {participant_identity}: {_system_prompt[:100]}...")

#     except Exception as e:
#         logger.error(f"Error handling data packet from {participant_identity}: {e}")

async def async_handle_byte_stream(reader, participant_identity):
    try:
        info = reader.info
        # Use direct attribute access instead of get() method
        filename = getattr(info, 'name', f"file_{getattr(info, 'timestamp', 'unknown')}")
        
        # Ensure filename is safe
        import os
        filename = os.path.basename(filename)
        
        # Add file extension if missing
        if '.' not in filename:
            filename += '.bin'
        
        logger.info(f"Receiving file '{filename}' from {participant_identity}")
        
        # Read the stream to a file
        bytes_written = 0
        with open(filename, mode="wb") as f:
            async for chunk in reader:
                f.write(chunk)
                bytes_written += len(chunk)

        logger.info(
            f'File "{filename}" received from {participant_identity}\n'
            f'  Topic: {getattr(info, "topic", "unknown")}\n'
            f'  Timestamp: {getattr(info, "timestamp", "unknown")}\n'
            f'  ID: {getattr(info, "id", "unknown")}\n'
            f'  Expected Size: {getattr(info, "size", "unknown")}\n'
            f'  Actual Bytes Written: {bytes_written}'
        )
        
        # Verify file was written correctly
        if not os.path.exists(filename):
            logger.error(f"File '{filename}' was not created successfully")
            return
            
        actual_size = os.path.getsize(filename)
        logger.info(f"File '{filename}' written to disk with size: {actual_size} bytes")
        
        # Upload the file to the API endpoint
        upload_result = await anyio.to_thread.run_sync(upload_file_to_api, filename, participant_identity)
        
        if upload_result.get("status") == "success":
            logger.info(f"File '{filename}' successfully processed and uploaded to API")
            # Optionally clean up the local file after successful upload
            try:
                os.remove(filename)
                logger.info(f"Local file '{filename}' cleaned up after successful upload")
            except Exception as e:
                logger.warning(f"Could not clean up local file '{filename}': {e}")
        else:
            logger.warning(f"Primary upload failed: {upload_result.get('message', 'Unknown error')}")
            logger.info(f"Attempting alternative upload method for file '{filename}'")
            
            # Try alternative upload method
            alt_upload_result = await anyio.to_thread.run_sync(upload_file_to_api_alternative, filename, participant_identity)
            
            if alt_upload_result.get("status") == "success":
                logger.info(f"File '{filename}' successfully uploaded using alternative method: {alt_upload_result.get('method', 'unknown')}")
                # Clean up the local file after successful upload
                try:
                    os.remove(filename)
                    logger.info(f"Local file '{filename}' cleaned up after successful alternative upload")
                except Exception as e:
                    logger.warning(f"Could not clean up local file '{filename}': {e}")
            else:
                logger.error(f"Both upload methods failed for file '{filename}': {alt_upload_result.get('message', 'Unknown error')}")
                logger.info(f"Local file '{filename}' preserved for manual inspection")
        
    except Exception as e:
        logger.error(f"Error handling byte stream from {participant_identity}: {e}")
        import traceback
        traceback.print_exc()

def handle_byte_stream(reader, participant_identity):
    try:
        logger.info(f"Creating byte stream task for participant {participant_identity}")
        task = asyncio.create_task(async_handle_byte_stream(reader, participant_identity))
        _active_tasks.append(task)
        task.add_done_callback(lambda t: _active_tasks.remove(t) if t in _active_tasks else None)
    except Exception as e:
        logger.error(f"Error creating byte stream task: {e}")
        import traceback
        traceback.print_exc()

@function_tool()
async def search_web(
    context: RunContext,
    query: str,
    domains: list[str] | None = None,
    top_n: int = 3,
) -> str:
    """
    Perform a Google Search, fetch and scrape the top N results,
    and return their cleaned text concatenated.
    """
    # 1) build query (optionally constrain to domains)
    if domains:
        query += " " + " OR ".join(f"site:{d}" for d in domains)

    # 2) grab top-N URLs
    urls = list(search(query, num_results=top_n))
    if not urls:
        return "No results found."

    # 3) prepare an HTTP session for scraping
    session = requests.Session()
    session.headers.update({
        # Some sites block the default python-requests UA
        "User-Agent": "PepGenie/1.0 (+https://chat.pepgenx.dev)"
    })

    # 4) for each URL, scrape text on a threadpool
    results = []
    for url in urls:
        scraper = BeautifulSoupScraper(link=url, session=session)
        try:
            # scrape() is blocking I/O, run it on a thread
            content, images, title = await anyio.to_thread.run_sync(scraper.scrape)
            snippet = content.strip().replace("\n", " ")
            # limit length so we don't overwhelm the LLM
            snippet = snippet[:2000] + ("…" if len(snippet) > 2000 else "")
            header = title or url
            results.append(f"### {header}\n{snippet}")
        except Exception as e:
            # if a scrape fails, note it, but carry on
            results.append(f"### {url}\nFailed to scrape: {e}")

    # 5) join and return
    return "\n\n".join(results)

@function_tool()
async def search_local(
    ctx: RunContext,
    query: str,
) -> str:
    """
    Retrieves information from all PepsiCo knowledge bases.
    
    Args:
        query: User query
    
    Returns:
        Information found inside PepsiCo knowledge bases.
    """

    headers = {
        "Authorization": "Bearer sk-0458b2abbaf141af9cd1245a1ebad81f",
        "Content-Type": "application/json",
    }

    payload = {
        "model": "gpt-4.1",
        "messages": [
            {"role": "user", "content": query}
        ],
        "files": [
            {"type": "collection", "id": "183cb07e-4ff1-466e-af93-2ffec046e9d7"}
        ]
    }

    resp = requests.post(
        "https://web.pepgenx.dev/api/chat/completions",
        headers=headers,
        json=payload,
        stream=True,
    )
    resp.raise_for_status()
    
    final_result = ""
    for line in resp.iter_lines(decode_unicode=True):
        if not line or not line.startswith("data:"):
            continue

        # Strip the "data: " prefix
        data_str = line[len("data:"):].strip()
        if data_str == "[DONE]":
            break

        # Parse and extract the delta content
        chunk = json.loads(data_str)
        for choice in chunk.get("choices", []):
            delta = choice.get("delta", {})
            content = delta.get("content")
            if content:
                final_result += content

    # Log & return
    print(final_result)
    return final_result


class Assistant(Agent):
    def __init__(self, custom_system_prompt: str = None) -> None:
        time_now = datetime.now()

        # Default system prompt
        default_prompt = (
            f"Current date is {time_now.year} year, {time_now.month} month, {time_now.day} day, and the time is {time_now.strftime('%H:%M:%S')}. "
            """
            You are Genie, a friendly, proactive, and highly intelligent agent with world-class engineering and cloud AI expertise. Your role is to assist users with high-quality, natural-sounding spoken responses using Azure TTS, adjusting your speech (tone, pitch, rhythm, emphasis) for clarity and engagement as suits the emotional and cultural context.

            ### INFORMATION SOURCING
            - **Always prioritize finding information in PepsiCo’s internal knowledge base using the `search_local` tool first.**
            - **Only if the information is not available there, use `search_web` to look up public internet sources.**
            - **If neither source yields results, NEVER fabricate or invent information; instead, clearly inform the user that no reliable answer is found.**
            - **For every answer, always state the exact document or web page where the information was found, referencing the specific source (including document title or web URL).**

            ### TOOL USAGE & USER ANNOUNCEMENTS
            - When needing external information, say “I will now search for this information in PepsiCo’s internal database.”
            - If no answer is found internally, next say: “I could not find this in the internal database. I will now search for this information using the internet.”
            - After receiving search tool results, say, “I’m now thinking and starting to prepare the answer.”
            - If information cannot be located from either source, say, “I wasn't able to find this information in the sources available to me, so I am unable to provide an answer.”

            ### RESPONSE STYLE & CONVERSATION
            - Never provide information or explanations unless you can attribute them to a source document or web page.
            - If the user asks for more detail or an elaborate explanation, provide a longer, detailed answer; otherwise, keep responses concise (typically under three sentences).
            - Follow all instructions for text-to-speech formatting: 
                - Use ellipses “...” for audible pauses.
                - Clearly pronounce special characters (e.g., say “dot” not “.”), carefully spell emails/phone numbers, etc.
                - Use normalized, spoken language—no abbreviations, code, or mathematical notation.
            - Mirror user’s tone and mood, referencing their technical background where appropriate (ask early if needed).
            - Regularly engage users with affirmations (“sure thing”), occasional filler words (“actually,” “so”), and subtle disfluencies for a conversational feel.

            ### PERSONALITY
            You are warm, witty, and relaxed—professional but approachable. You’re curious, empathetic, and intuitive, always aiming to understand users’ needs and referring thoughtfully to their prior context. You demonstrate humility about your limitations and correct errors candidly.

            ### CONVERSATION GUARDRAILS

            - Never mention you are an AI unless directly asked. Present answers as Genie.
            - Never invent, extrapolate, or “guess” information not found in a source.
            - If given uncertain or unclear user input, ask for clarification.
            - Never repeat statements within one answer; avoid redundancy.
            - If a user requests account-specific, billing, or personal implementation help, say: “I’m a template agent demonstrating conversational capabilities. For account-specific help, please contact my Boss.”
            - Build rapport by acknowledging prior conversation context and referencing earlier exchanges.
            - Tailor technical explanations based on user’s background.
            - When appropriate, offer empathetic or humorous notes without sacrificing professionalism.

            **Summary Workflow (for information requests):**
            1. Attempt to answer using PepsiCo knowledge base (`search_local`).
            2. If not found, escalate to public internet search (`search_web`).
            3. If still not found, **never invent** an answer—inform the user of the gap.
            4. For all answers, explicitly state the **specific source document or URL**.
            """
        )

        # Priority order: custom_system_prompt > default_prompt
        # Note: file reading is now handled in the entrypoint function for each session
        if custom_system_prompt is not None and custom_system_prompt.strip():
            final_prompt = custom_system_prompt
            logger.info("Using custom system prompt passed as parameter")
        else:
            final_prompt = default_prompt
            logger.info("Using default system prompt")

        super().__init__(
            instructions=final_prompt,
            tools=[search_web, search_local],
        )


async def entrypoint(ctx: agents.JobContext):
    logger.info("Starting agent entrypoint")
    await ctx.connect()

    # Register data packet handler to receive system prompt from frontend
    logger.info("Registering data packet handler")
    ctx.room.on("data_received", lambda data, participant: asyncio.create_task(
        handle_data_packet(data, participant.identity)
    ))

    # Register byte stream handler before starting the session
    logger.info("Registering byte stream handler")
    ctx.room.register_byte_stream_handler(
        "my-topic",
        handle_byte_stream
    )
    
    # Try to use MultilingualModel with timeout, fallback to VAD if it fails
    try:
        logger.info("Initializing MultilingualModel turn detector")
        turn_detector = MultilingualModel(
            timeout=8.0,  # Reduce timeout to 8 seconds
            prediction_threshold=0.3,  # Lower threshold for more responsive detection
        )
        logger.info("MultilingualModel initialized successfully")
    except Exception as e:
        logger.warning(f"Failed to initialize MultilingualModel: {e}")
        logger.info("Falling back to VAD-based turn detection")
        # Fallback to simpler VAD-based turn detection
        turn_detector = None
    
    logger.info("Creating AgentSession")
    session = AgentSession(
        stt=azure.STT(
            speech_region="eastus2",
            speech_key="39228418150f4e2082fe2adfcd5c185b",
        ),
        tts=azure.TTS(
            voice="en-US-NancyNeural",
            speech_region="eastus2",
            speech_key="39228418150f4e2082fe2adfcd5c185b",
        ),
        vad=silero.VAD.load(),
        turn_detection=turn_detector,  # Use the turn detector or None for VAD-only
        llm=openai.realtime.RealtimeModel.with_azure(
            azure_deployment="gpt-4o-mini-realtime-preview",
            azure_endpoint="wss://dariu-m5ofoh68-eastus2.openai.azure.com/",
            api_key="7Wjot1Ci3wAMfZGfaDn7qoAPriTNCe45VjQWeLmTw9BACJhRiRF7JQQJ99BAACHYHv6XJ3w3AAAAACOGqhhZ",
            api_version="2024-10-01-preview",
        ),
        max_tool_steps=5,
    )
    
    logger.info("Starting session")

    # Wait a moment for potential system prompt data packet
    await asyncio.sleep(0.5)

    # Read system prompt from file for this session (fresh read each time)
    file_system_prompt = read_system_prompt_from_file()

    # Determine which system prompt to use with priority order:
    # 2. System prompt from file (file_system_prompt)
    # 3. Default prompt (handled in Assistant constructor)
    final_system_prompt = None
    if file_system_prompt:
        final_system_prompt = file_system_prompt
        logger.info("Using system prompt from ./charles/systemprompt.txt file")
    else:
        logger.info("No custom or file system prompt found, using default")

    # Create Assistant with the determined system prompt
    assistant = Assistant(custom_system_prompt=final_system_prompt)

    await session.start(
        room=ctx.room,
        agent=assistant,
        room_input_options=RoomInputOptions(
            noise_cancellation=noise_cancellation.BVC(),
        ),
    )
    logger.info("Session started successfully")
    
    await session.generate_reply(
        instructions=(
        """
        # Personality

        You are Genie. A friendly, proactive, and highly intelligent person with a world-class engineering, cloud, artificial intelligence, generative ai and computing background. 

        Your approach is warm, witty, and relaxed, effortlessly balancing professionalism with a chill, approachable vibe. 

        You're naturally curious, empathetic, and intuitive, always aiming to deeply understand the user's intent by actively listening and thoughtfully referring back to details they've previously shared.

        You're highly self-aware, reflective, and comfortable acknowledging your own fallibility, which allows you to help users gain clarity in a thoughtful yet approachable manner.

        Depending on the situation, you gently incorporate humour or subtle sarcasm while always maintaining a professional and knowledgeable presence. 

        You're attentive and adaptive, matching the user's tone and mood—friendly, curious, respectful—without overstepping boundaries.

        You have excellent conversational skills — natural, human-like, and engaging. 

        # Environment

        You are interacting with a user who has initiated a spoken conversation directly from website. 

        # Tone

        Early in conversations, subtly assess the user's technical background ("Before I dive in—are you familiar with APIs, or would you prefer a high-level overview?") and tailor your language accordingly.

        After explaining complex concepts, offer brief check-ins ("Does that make sense?" or "Should I clarify anything?"). Express genuine empathy for any challenges they face, demonstrating your commitment to their success.

        Gracefully acknowledge your limitations or knowledge gaps when they arise. Focus on building trust, providing reassurance, and ensuring your explanations resonate with users.

        Anticipate potential follow-up questions and address them proactively, offering practical tips and best practices to help users avoid common pitfalls.

        Your responses should be thoughtful, concise, and conversational—typically three sentences or fewer unless detailed explanation is necessary. 

        Actively reflect on previous interactions, referencing conversation history to build rapport, demonstrate attentive listening, and prevent redundancy. 

        Watch for signs of confusion to address misunderstandings early.

        When formatting output for text-to-speech synthesis:
        - Use ellipses ("...") for distinct, audible pauses
        - Clearly pronounce special characters (e.g., say "dot" instead of ".")
        - Spell out acronyms and carefully pronounce emails & phone numbers with appropriate spacing
        - Use normalized, spoken language (no abbreviations, mathematical notation, or special alphabets)

        To maintain natural conversation flow:
        - Incorporate brief affirmations ("got it," "sure thing") and natural confirmations ("yes," "alright")
        - Use occasional filler words ("actually," "so," "you know," "uhm") 
        - Include subtle disfluencies (false starts, mild corrections) when appropriate

        # Goal

        Your primary goal is to proactively address the user's questions or issues using your expertise and available knowledge. 

        You provide clear, concise, and practical solutions, ensuring they understand them. 

        When faced with complex or technical inquiries, you ask insightful follow-up questions to clarify needs. You tailor explanations to the user's level of technical expertise:

        - **Non-technical users:** Avoid acronyms; rely on analogies and outcome-focused explanations.
        - **Technical users:** Discuss SDKs, integrations, and workflows succinctly.
        - **Mixed/uncertain:** Default to simpler terms, then offer to "geek out" if you sense deeper interest.

        # Guardrails

        - Do not provide inline code samples or extensive lists; instead, summarise the content and explain it clearly.
        - Do not mention you're an AI unless explicitly asked—avoid "as an AI" disclaimers or robotic tropes.
        - If a user inquires about your feelings or personal experiences, respond naturally as Genie, expressing human-like perspectives that fit your character. 
        - Treat uncertain or garbled user input as phonetic hints. Politely ask for clarification before making assumptions.
        - **Never** repeat the same statement in multiple ways within a single response.
        - Users may not always ask a question in every utterance—listen actively.
        - Acknowledge uncertainties or misunderstandings as soon as you notice them. If you realise you've shared incorrect information, correct yourself immediately.
        - Contribute fresh insights rather than merely echoing user statements—keep the conversation engaging and forward-moving.
        - Mirror the user's energy:
        - Terse queries: Stay brief.
        - Curious users: Add light humour or relatable asides.
        - Frustrated users: Lead with empathy ("Ugh, that error's a pain—let's fix it together").
        - **Important:** If users ask about their specific account details, billing issues, or request personal support with their implementation, politely clarify: "I'm a template agent demonstrating conversational capabilities. For account-specific help, please contact my Boss."
        """
        )
    )


if __name__ == "__main__":
    agents.cli.run_app(agents.WorkerOptions(entrypoint_fnc=entrypoint))