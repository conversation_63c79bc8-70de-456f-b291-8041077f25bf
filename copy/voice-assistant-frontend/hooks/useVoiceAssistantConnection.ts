import { useCallback, useState } from "react";
import type { ConnectionDetails } from "@/app/api/connection-details/route";

interface UseVoiceAssistantConnectionReturn {
  connect: () => Promise<void>;
  isConnecting: boolean;
  connectionDetails: ConnectionDetails | null;
  error: Error | null;
}

export default function useVoiceAssistantConnection(): UseVoiceAssistantConnectionReturn {
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionDetails, setConnectionDetails] = useState<ConnectionDetails | null>(null);
  const [error, setError] = useState<Error | null>(null);

  const connect = useCallback(async () => {
    setIsConnecting(true);
    setError(null);

    try {
      const response = await fetch("/api/connection-details");
      
      if (!response.ok) {
        throw new Error(`Failed to get connection details: ${response.statusText}`);
      }

      const data = await response.json();
      setConnectionDetails(data);
    } catch (err) {
      console.error("Error connecting to voice assistant:", err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setIsConnecting(false);
    }
  }, []);

  return {
    connect,
    isConnecting,
    connectionDetails,
    error,
  };
}