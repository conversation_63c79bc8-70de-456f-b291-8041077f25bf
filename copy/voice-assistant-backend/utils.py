# utils.py
from bs4 import BeautifulSoup
import re
from typing import List, Dict, Any
from urllib.parse import urljoin

def clean_soup(soup: BeautifulSoup) -> BeautifulSoup:
    """Remove unwanted tags and content from the BeautifulSoup object."""
    # Remove script, style, and other non-content tags
    for tag in soup.select('script, style, meta, link, noscript, iframe, svg'):
        tag.decompose()
    
    # Remove common ads and navigation elements
    selectors = [
        'header', 'footer', 'nav', '.nav', '.navigation', '.menu',
        '.ad', '.ads', '.advertisement', '.sidebar', '.comments',
        '#header', '#footer', '#nav', '#sidebar', '#comments',
        '[class*="cookie"]', '[class*="popup"]', '[class*="banner"]',
        '[id*="cookie"]', '[id*="popup"]', '[id*="banner"]'
    ]
    for selector in selectors:
        for element in soup.select(selector):
            element.decompose()
    
    return soup

def get_text_from_soup(soup: BeautifulSoup) -> str:
    """Extract meaningful text from the soup."""
    # Get all paragraphs and headings
    content_elements = soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'pre', 'code'])
    
    # Join the text with appropriate spacing
    content = []
    for element in content_elements:
        text = element.get_text(strip=True)
        if text:  # Only include non-empty text
            tag_name = element.name
            
            # Add formatting based on the tag
            if tag_name.startswith('h'):
                content.append(f"\n{text}\n")
            elif tag_name == 'li':
                content.append(f"• {text}")
            elif tag_name in ['pre', 'code']:
                content.append(f"\n{text}\n")
            else:
                content.append(text)
    
    # Clean up the text - remove excessive newlines and spaces
    text = '\n'.join(content)
    text = re.sub(r'\n{3,}', '\n\n', text)
    text = re.sub(r' {2,}', ' ', text)
    
    return text.strip()

def extract_title(soup: BeautifulSoup) -> str:
    """Extract the title of the page."""
    if soup.title:
        return soup.title.get_text(strip=True)
    
    # Try to find the main heading if title tag is not available
    for heading in soup.find_all(['h1', 'h2']):
        text = heading.get_text(strip=True)
        if text:
            return text
    
    return ""

def get_relevant_images(soup: BeautifulSoup, base_url: str) -> List[Dict[str, Any]]:
    """
    Extract images from the soup and score them based on size, position, etc.
    Returns list of dicts with 'url' and 'score' keys.
    """
    images = []
    
    for img in soup.find_all('img'):
        src = img.get('src', '')
        if not src:
            continue
        
        # Make relative URLs absolute
        img_url = urljoin(base_url, src)
        
        # Simple scoring based on available attributes
        score = 0
        
        # Width and height as score indicators (bigger is generally more important)
        width = img.get('width')
        height = img.get('height')
        if width and height:
            try:
                width = int(width)
                height = int(height)
                if width > 200 and height > 200:
                    score += 5
                elif width > 100 and height > 100:
                    score += 3
            except (ValueError, TypeError):
                pass
        
        # Alt text presence is a good indicator of meaningful images
        if img.get('alt'):
            score += 2
        
        # Position scoring - earlier images are often more relevant
        if len(images) < 3:
            score += 2
        
        # Filter out very small or likely icon images
        if score > 0:
            images.append({
                'url': img_url,
                'score': score
            })
    
    # Sort by score (highest first)
    return sorted(images, key=lambda x: x['score'], reverse=True)
