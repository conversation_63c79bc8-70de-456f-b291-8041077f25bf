import { NextRequest, NextResponse } from "next/server";

// don't cache the results
export const revalidate = 0;

const UPLOAD_API_URL = "https://web.pepgenx.dev/api/v1/knowledge/183cb07e-4ff1-466e-af93-2ffec046e9d7/file/add";
const API_TOKEN = process.env.PEPGENX_API_TOKEN || "sk-0458b2abbaf141af9cd1245a1ebad81f";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Validate file size (optional - adjust as needed)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File size too large. Maximum size is 10MB." },
        { status: 400 }
      );
    }

    // Convert File to Buffer for Node.js compatibility
    const fileArrayBuffer = await file.arrayBuffer();
    const fileBuffer = Buffer.from(fileArrayBuffer);

    // Create FormData with proper file handling for Node.js
    const externalFormData = new FormData();
    // Use Blob instead of File (which is not available in Node.js)
    const fileBlob = new Blob([fileBuffer], { type: file.type || 'application/octet-stream' });
    externalFormData.append("file", fileBlob, file.name);

    console.log(`Uploading file: ${file.name}, size: ${file.size}, type: ${file.type}`);
    console.log(`API endpoint: ${UPLOAD_API_URL}`);
    console.log(`Using API token: ${API_TOKEN ? 'Token present' : 'No token'}`);

    // Forward the request to the external API
    const response = await fetch(UPLOAD_API_URL, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${API_TOKEN}`,
        // Don't set Content-Type - let fetch handle it for FormData
      },
      body: externalFormData,
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => "Unknown error");
      console.error("External API error:", {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        body: errorText
      });

      return NextResponse.json(
        {
          error: `Upload failed: ${response.status} ${response.statusText}`,
          details: errorText
        },
        { status: response.status }
      );
    }

    const result = await response.json().catch(async () => {
      // If JSON parsing fails, try to get text response
      const text = await response.text();
      return { message: "File uploaded successfully", response: text };
    });

    return NextResponse.json(
      { 
        success: true, 
        message: "File uploaded successfully",
        data: result 
      },
      { 
        status: 200,
        headers: {
          "Cache-Control": "no-store",
        },
      }
    );

  } catch (error) {
    console.error("Upload error:", error);
    
    return NextResponse.json(
      { 
        error: "Internal server error during file upload",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
