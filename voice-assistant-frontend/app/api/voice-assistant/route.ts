import { NextResponse } from "next/server";

// don't cache the results
export const revalidate = 0;

export async function GET() {
  try {
    // For now, just return a simple response to fix the build error
    // This can be expanded later with actual voice assistant API functionality
    return NextResponse.json(
      { message: "Voice assistant API endpoint" },
      {
        headers: {
          "Cache-Control": "no-store",
        },
      }
    );
  } catch (error) {
    if (error instanceof Error) {
      console.error(error);
      return new NextResponse(error.message, { status: 500 });
    }
    return new NextResponse("An unknown error occurred", { status: 500 });
  }
}