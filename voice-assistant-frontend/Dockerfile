FROM node:22-alpine AS base

# Install pnpm
RUN corepack enable && corepack prepare pnpm@9.15.9 --activate

# Set working directory
WORKDIR /app

# Install dependencies
FROM base AS dependencies
COPY package.json pnpm-lock.yaml* ./
RUN pnpm install

# Build the application
FROM dependencies AS builder
COPY . .
RUN pnpm build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV PORT=3000

# Copy necessary files from builder
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static/

# Create public directory
RUN mkdir -p ./public

# Copy public directory contents if they exist
# Using separate RUN command with shell to check if directory exists and has files
RUN if [ -d /app/public ] && [ "$(ls -A /app/public)" ]; then \
      cp -R /app/public/. ./public/; \
    fi

EXPOSE 3000

CMD ["node", "server.js"]
