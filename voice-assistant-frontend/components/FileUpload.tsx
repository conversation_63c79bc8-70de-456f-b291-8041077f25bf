"use client";

import { AnimatePresence, motion } from "framer-motion";
import { useCallback, useRef, useState, useContext } from "react";
import { RoomContext } from "@livekit/components-react";

interface UploadStatus {
  status: "idle" | "uploading" | "success" | "error";
  message?: string;
  progress?: number;
}

export function FileUpload() {
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>({ status: "idle" });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const room = useContext(RoomContext);

  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!room) {
      setUploadStatus({ 
        status: "error", 
        message: "Room not connected" 
      });
      return;
    }

    setUploadStatus({ status: "uploading", progress: 0 });

    try {
      // Send file using LiveKit's sendFile method
      const info = await room.localParticipant.sendFile(file, {
        mimeType: file.type,
        topic: 'my-topic',
        // Optional, allows progress to be shown to the user
        onProgress: (progress) => {
          console.log('sending file, progress', Math.ceil(progress * 100));
          setUploadStatus({ status: "uploading", progress });
        }
      });
      
      console.log(`Sent file with stream ID: ${info.id}`);
      
      setUploadStatus({
        status: "success",
        message: `File "${file.name}" sent successfully!`
      });

      // Clear the input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }

      // Auto-hide success message after 3 seconds
      setTimeout(() => {
        setUploadStatus({ status: "idle" });
      }, 3000);

    } catch (error) {
      console.error("File send error:", error);
      setUploadStatus({ 
        status: "error", 
        message: error instanceof Error ? error.message : "File send failed" 
      });

      // Auto-hide error message after 5 seconds
      setTimeout(() => {
        setUploadStatus({ status: "idle" });
      }, 5000);
    }
  }, [room]);

  const handleButtonClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const getButtonText = () => {
    switch (uploadStatus.status) {
      case "uploading":
        return "Sending...";
      case "success":
        return "File Sent!";
      case "error":
        return "Send Failed";
      default:
        return "Send File";
    }
  };

  const getButtonStyles = () => {
    const baseStyles = "px-4 py-2 rounded-md font-medium transition-all duration-200 disabled:cursor-not-allowed";
    
    switch (uploadStatus.status) {
      case "uploading":
        return `${baseStyles} bg-blue-600 text-white cursor-not-allowed`;
      case "success":
        return `${baseStyles} bg-green-600 text-white`;
      case "error":
        return `${baseStyles} bg-red-600 text-white hover:bg-red-700`;
      default:
        return `${baseStyles} bg-white text-black hover:bg-gray-100`;
    }
  };

  return (
    <div className="flex flex-col items-center gap-2">
      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileSelect}
        className="hidden"
        accept="*/*"
      />
      
      <motion.button
        onClick={handleButtonClick}
        disabled={uploadStatus.status === "uploading"}
        className={getButtonStyles()}
        whileHover={uploadStatus.status === "idle" ? { scale: 1.02 } : {}}
        whileTap={uploadStatus.status === "idle" ? { scale: 0.98 } : {}}
        transition={{ duration: 0.2 }}
      >
        {getButtonText()}
      </motion.button>

      <AnimatePresence>
        {uploadStatus.message && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className={`text-sm px-3 py-1 rounded-md ${
              uploadStatus.status === "success" 
                ? "bg-green-100 text-green-800" 
                : "bg-red-100 text-red-800"
            }`}
          >
            {uploadStatus.message}
          </motion.div>
        )}
      </AnimatePresence>

      {uploadStatus.status === "uploading" && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="w-32 h-1 bg-gray-200 rounded-full overflow-hidden"
        >
          <motion.div
            className="h-full bg-blue-600"
            initial={{ width: "0%" }}
            animate={{ width: `${(uploadStatus.progress || 0) * 100}%` }}
            transition={{ duration: 0.2, ease: "easeInOut" }}
          />
        </motion.div>
      )}
    </div>
  );
}
